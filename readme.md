好的，遵照你的要求，我将我们之前讨论的产品功能清单和技术实现方案重新整理成一份清晰、完整的综合文档。

这份文档将作为你项目的核心蓝图，涵盖了从产品设计到技术落地的所有关键环节。

---

### **简易号池管理系统 (EasyPool) - 综合方案**

---

### **第一部分：产品规划 (Product Plan)**

#### **1. 产品概述**

*   **产品名称:** 简易号池管理系统 (EasyPool)
*   **产品定位:** 一个超轻量级的内部账号资源池，由单一管理员通过界面维护，由自动化程序通过API进行调用，解决了账号管理混乱、状态不清、取用冲突的问题。
*   **核心用户:**
    *   **界面使用者 (管理员/运营):** 负责分类、入库和维护账号资源。
    *   **API 调用者 (开发者/自动化程序):** 负责以编程方式获取和上报账号状态。
*   **核心工作流:**
    1.  **分类:** 管理员创建 `号池类型` (如: "A业务账号")。
    2.  **建池:** 在类型下创建具体的 `号池` (如: "A业务-新用户")。
    3.  **入库:** 向号池中批量或单个上传账号。
    4.  **取号:** 程序通过API从指定号池获取一个可用账号，该账号状态变为 `占用中`。
    5.  **归还:** 程序使用完毕后，通过API上报结果，将账号释放回池 (`可用`) 或标记为 `失效`。

#### **2. V1.0 核心功能清单 (MVP)**

**2.1 核心概念**

*   **号池类型 (Pool Type):** 账号池的顶级分类，用于隔离不同业务场景。
*   **号池 (Pool):** 具体的账号容器，隶属于一个号池类型。
*   **账号 (Account):** 号池中的基本单元。
*   **账号状态 (Account Status):**
    *   `可用 (Available)`: 健康，可被取用。
    *   `占用中 (In-Use)`: 已被取出，临时锁定。
    *   `失效 (Invalid)`: 已确认无法使用。
    *   `已过期 (Expired)`: 超过有效期。

**2.2 界面功能 (Web UI)**
*   **访问方式:** 无需登录，直接访问固定URL进行管理。

| 模块           | 功能点                                                                                                  | 描述                                                                           |
| :------------- | :------------------------------------------------------------------------------------------------------ | :----------------------------------------------------------------------------- |
| **类型管理**   | **创建类型、列表展示**                                                                                  | 在主界面创建和查看所有号池类型。                                               |
| **号池管理**   | **按类型分组展示、创建号池**                                                                            | 主界面按类型分组展示号池卡片，卡片显示名称、总数、可用数。支持在类型下创建新号池。 |
| **账号管理**   | **账号列表**                                                                                            | 点击号池卡片，进入详情页，表格展示账号内容(部分脱敏)、状态、过期时间等。       |
|                | **添加单个账号**                                                                                        | 表单形式，输入账号内容（支持JSON）、设置过期时间、备注。                       |
|                | **批量上传账号**                                                                                        | 支持上传CSV文件批量导入账号，并提供模板下载。                                  |
|                | **核心操作**                                                                                            | 支持 **删除账号** 和 **强制释放** (将“占用中”的号改回“可用”)。                  |
| **API密钥管理** | **展示、复制、重新生成**                                                                                | 界面固定位置显示唯一的API Key，提供一键复制和重新生成功能（带二次确认）。        |

**2.3 API 接口 (for Developers)**
*   **认证:** 所有请求在HTTP Header中携带 `X-API-KEY: [你的API密钥]`。

| Method | Endpoint                    | 描述                                                                                                     |
| :----- | :-------------------------- | :------------------------------------------------------------------------------------------------------- |
| `GET`  | `/api/v1/account`           | **获取账号**。需传入`pool_name`参数。成功则返回账号ID和内容，并将其状态锁定为`占用中`。无可用账号则返回404。 |
| `POST` | `/api/v1/account/report`    | **上报账号状态**。需传入`account_id`和`result`("ok"或"failed")。`ok`则释放回池，`failed`则标记为失效。    |

---

### **第二部分：技术实现方案 (Technical Plan)**

#### **1. 整体架构与技术栈**

*   **架构模式:** **Nuxt 3 全栈模式** (前端与后端API在同一项目中)。
*   **技术栈:**
    *   **前端:** Nuxt 3, Vue 3, TypeScript, Tailwind CSS
    *   **后端:** Nuxt 3 / Nitro (内置服务器引擎)
    *   **ORM:** Prisma
    *   **数据库:** **TiDB Cloud** (兼容MySQL协议)

#### **2. 数据库设计 (Prisma Schema)**

*   **文件位置:** `prisma/schema.prisma`
*   **核心逻辑:** 使用`mysql`提供者连接 TiDB Cloud，并启用`relationMode = "prisma"`以适应其Serverless特性。

```prisma
// prisma/schema.prisma

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider     = "mysql"
  url          = env("DATABASE_URL")
  relationMode = "prisma" // 推荐用于TiDB Cloud Serverless
}

enum AccountStatus {
  Available, InUse, Invalid, Expired
}

model PoolType {
  id    String @id @default(cuid())
  name  String @unique
  pools Pool[]
}

model Pool {
  id         String   @id @default(cuid())
  name       String   @unique
  poolTypeId String
  poolType   PoolType @relation(fields: [poolTypeId], references: [id], onDelete: Cascade)
  accounts   Account[]
}

model Account {
  id         String        @id @default(cuid())
  content    String @db.Text // 使用Text类型以支持较长的JSON字符串
  status     AccountStatus @default(Available)
  expiresAt  DateTime?
  notes      String?    @db.Text
  poolId     String
  pool       Pool          @relation(fields: [poolId], references: [id], onDelete: Cascade)
  createdAt  DateTime      @default(now())
  updatedAt  DateTime      @updatedAt
  lastUsedAt DateTime?
}

model ApiKey {
  id        Int      @id @default(autoincrement())
  key       String   @unique @default(cuid())
  createdAt DateTime @default(now())
}
```

#### **3. 核心实现细节**

**3.1 后端实现 (Nitro Server)**

*   **目录结构:** 业务逻辑位于 `server/api/` 目录下。
*   **数据库连接:**
    *   在 `.env` 文件中配置从 TiDB Cloud 获取的 `DATABASE_URL`。
      ```
      # .env
      DATABASE_URL="mysql://user:password@host:port/database?sslaccept=strict"
      ```
    *   在 `server/utils/db.ts` 中创建 Prisma Client 单例，避免在Serverless环境中耗尽连接。
*   **原子化取号 (防止并发冲突):**
    *   在 `GET /api/v1/account` 接口中，使用 Prisma 的 `$transaction` 来保证“查找并锁定”操作的原子性。

**3.2 前端实现 (Vue 3 / Nuxt 3)**

*   **页面路由:**
    *   `pages/index.vue`: 主页，展示所有号池类型和号池。
    *   `pages/pool/[id].vue`: 号池详情页，管理特定号池下的账号。
*   **组件化:**
    *   将UI拆分为可复用的组件，如 `PoolCard.vue`, `AccountTable.vue`, `AddAccountModal.vue`。
*   **数据获取:**
    *   使用 Nuxt 3 内置的 `useFetch` 组合式函数与后端API进行交互。
*   **状态管理:**
    *   使用 `Pinia` 来管理全局状态，如API Key，方便在不同组件间共享。

#### **4. 开发与部署工作流**

1.  **环境搭建:**
    *   初始化 Nuxt 3 项目: `npx nuxi@latest init easy-pool`。
    *   安装依赖: `npm install @prisma/client` 和 `tailwindcss` 相关模块。
    *   初始化 Prisma: `npx prisma init`。
2.  **配置:**
    *   将上述 `schema.prisma` 内容填入对应文件。
    *   在 `.env` 文件中配置好 TiDB Cloud 的 `DATABASE_URL`。
3.  **数据库初始化:**
    *   运行 `npx prisma db push` 将数据模型推送到 TiDB Cloud，自动创建表结构。
4.  **开发:**
    *   运行 `npm run dev` 启动全栈开发服务器，进行实时编码和调试。
5.  **部署:**
    *   **推荐:** 使用 **Vercel** 或 **Netlify**。将项目推送到GitHub，连接Vercel账户，平台将自动完成构建和部署。需要在Vercel项目设置中配置好 `DATABASE_URL` 环境变量。
    *   **备选:** 使用 **Docker**。编写 `Dockerfile` 打包 Nuxt 3 应用，然后部署到任何支持容器的云服务器上。